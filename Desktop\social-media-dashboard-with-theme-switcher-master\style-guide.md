# Front-end Style Guide

## Layout

The designs were created to the following widths:

- Mobile: 375px
- Desktop: 1440px

> 💡 These are just the design sizes. Ensure content is responsive and meets WCAG requirements by testing the full range of screen sizes from 320px to large screens.

## Colors

### Primary

- Lime Green: hsl(163, 72%, 41%)
- Bright Red: hsl(356, 69%, 56%)

- Facebook: hsl(208, 92%, 53%)
- Twitter: hsl(203, 89%, 53%)
- Instagram: linear gradient hsl(37, 97%, 70%) to hsl(329, 70%, 58%)
- YouTube: hsl(348, 97%, 39%)

#### Dark Theme

- Toggle: linear gradient hsl(210, 78%, 56%) to hsl(146, 68%, 55%)

#### Light Theme

- Toggle: hsl(230, 22%, 74%)

### Neutral

#### Dark Theme

- Very Dark Blue (BG): hsl(230, 17%, 14%)
- Very Dark Blue (Top BG Pattern): hsl(232, 19%, 15%)
- Dark Desaturated Blue (Card BG): hsl(228, 28%, 20%)
- Desaturated Blue (Text): hsl(228, 34%, 66%)
- White (Text): hsl(0, 0%, 100%)

#### Light Theme

- White (BG): hsl(0, 0%, 100%)
- Very Pale Blue (Top BG Pattern): hsl(225, 100%, 98%)
- Light Grayish Blue (Card BG): hsl(227, 47%, 96%)
- Dark Grayish Blue (Text): hsl(228, 12%, 44%)
- Very Dark Blue (Text): hsl(230, 17%, 14%)

## Typography

### Body Copy

- Font size (Overview Card Headings): 14px

### Font

- Family: [Inter](https://fonts.google.com/specimen/Inter)
- Weights: 400, 700

> 💎 [Upgrade to Pro](https://www.frontendmentor.io/pro?ref=style-guide) for design file access to see all design details and get hands-on experience using a professional workflow with tools like Figma.
