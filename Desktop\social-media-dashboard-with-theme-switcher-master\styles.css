/* CSS Custom Properties for Theme Variables */
:root {
  /* Light Theme Colors */
  --bg-color: hsl(0, 0%, 100%);
  --top-bg-pattern: hsl(225, 100%, 98%);
  --card-bg: hsl(227, 47%, 96%);
  --text-primary: hsl(230, 17%, 14%);
  --text-secondary: hsl(228, 12%, 44%);
  --toggle-bg: hsl(230, 22%, 74%);
  
  /* Social Media Colors */
  --facebook: hsl(208, 92%, 53%);
  --twitter: hsl(203, 89%, 53%);
  --instagram-start: hsl(37, 97%, 70%);
  --instagram-end: hsl(329, 70%, 58%);
  --youtube: hsl(348, 97%, 39%);
  
  /* Status Colors */
  --lime-green: hsl(163, 72%, 41%);
  --bright-red: hsl(356, 69%, 56%);
}

/* Dark Theme */
[data-theme="dark"] {
  --bg-color: hsl(230, 17%, 14%);
  --top-bg-pattern: hsl(232, 19%, 15%);
  --card-bg: hsl(228, 28%, 20%);
  --text-primary: hsl(0, 0%, 100%);
  --text-secondary: hsl(228, 34%, 66%);
  --toggle-bg: linear-gradient(hsl(210, 78%, 56%), hsl(146, 68%, 55%));
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', sans-serif;
  background-color: var(--bg-color);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
  min-height: 100vh;
}

.container {
  max-width: 1110px;
  margin: 0 auto;
  padding: 0 25px;
  position: relative;
}

/* Background Pattern */
.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 244px;
  background-color: var(--top-bg-pattern);
  border-radius: 0 0 20px 20px;
  z-index: -1;
  transition: background-color 0.3s ease;
}

/* Header Styles */
.header {
  padding: 36px 0 46px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 4px;
  color: var(--text-primary);
}

.subtitle {
  font-size: 14px;
  font-weight: 700;
  color: var(--text-secondary);
}

/* Theme Toggle */
.theme-toggle {
  display: flex;
  align-items: center;
  gap: 13px;
}

.toggle-label {
  font-size: 14px;
  font-weight: 700;
  color: var(--text-secondary);
}

.toggle-switch {
  position: relative;
  width: 48px;
  height: 24px;
  background: var(--toggle-bg);
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.toggle-slider {
  position: absolute;
  top: 3px;
  left: 3px;
  width: 18px;
  height: 18px;
  background: var(--card-bg);
  border-radius: 50%;
  transition: transform 0.3s ease, background-color 0.3s ease;
}

[data-theme="dark"] .toggle-slider {
  transform: translateX(24px);
}

.toggle-switch:hover {
  background: linear-gradient(hsl(210, 78%, 56%), hsl(146, 68%, 55%));
}

/* Social Cards Grid */
.social-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(255px, 1fr));
  gap: 30px;
  margin-bottom: 46px;
}

.social-card {
  background: var(--card-bg);
  border-radius: 5px;
  padding: 32px 24px 24px;
  text-align: center;
  position: relative;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.2s ease;
  overflow: hidden;
}

.social-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.social-card.facebook::before {
  background: var(--facebook);
}

.social-card.twitter::before {
  background: var(--twitter);
}

.social-card.instagram::before {
  background: linear-gradient(to right, var(--instagram-start), var(--instagram-end));
}

.social-card.youtube::before {
  background: var(--youtube);
}

.social-card:hover {
  background: var(--text-secondary);
  transform: translateY(-5px);
}

[data-theme="dark"] .social-card:hover {
  background: hsl(228, 25%, 27%);
}

.social-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 28px;
}

.social-icon {
  width: 20px;
  height: 20px;
}

.username {
  font-size: 12px;
  font-weight: 700;
  color: var(--text-secondary);
}

.follower-count {
  font-size: 56px;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
  margin-bottom: 9px;
}

.follower-label {
  font-size: 12px;
  font-weight: 400;
  color: var(--text-secondary);
  letter-spacing: 5px;
  margin-bottom: 25px;
}

.daily-change {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 700;
}

.daily-change.positive {
  color: var(--lime-green);
}

.daily-change.negative {
  color: var(--bright-red);
}

.arrow-icon {
  width: 8px;
  height: 4px;
}

/* Overview Section */
.overview {
  margin-bottom: 45px;
}

.overview-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-secondary);
  margin-bottom: 27px;
}

.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(255px, 1fr));
  gap: 16px;
}

.overview-card {
  background: var(--card-bg);
  border-radius: 5px;
  padding: 26px 24px 19px;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.2s ease;
}

.overview-card:hover {
  background: var(--text-secondary);
  transform: translateY(-5px);
}

[data-theme="dark"] .overview-card:hover {
  background: hsl(228, 25%, 27%);
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 21px;
}

.metric-name {
  font-size: 14px;
  font-weight: 700;
  color: var(--text-secondary);
}

.metric-icon {
  width: 20px;
  height: 20px;
}

.overview-stats {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.metric-value {
  font-size: 32px;
  font-weight: 700;
  color: var(--text-primary);
}

.metric-change {
  display: flex;
  align-items: center;
  gap: 3px;
  font-size: 12px;
  font-weight: 700;
}

.metric-change.positive {
  color: var(--lime-green);
}

.metric-change.negative {
  color: var(--bright-red);
}

/* Attribution */
.attribution {
  font-size: 11px;
  text-align: center;
  padding: 20px 0;
  color: var(--text-secondary);
}

.attribution a {
  color: hsl(228, 45%, 44%);
  text-decoration: none;
}

.attribution a:hover {
  text-decoration: underline;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .container {
    padding: 0 24px;
  }
  
  .container::before {
    height: 235px;
  }
  
  .header {
    padding: 36px 0 40px;
  }
  
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .theme-toggle {
    align-self: stretch;
    justify-content: space-between;
    padding-top: 16px;
    border-top: 1px solid var(--text-secondary);
  }
  
  .title {
    font-size: 24px;
  }
  
  .social-cards {
    grid-template-columns: 1fr;
    gap: 24px;
    margin-bottom: 45px;
  }
  
  .overview-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .overview-title {
    font-size: 24px;
  }
}
